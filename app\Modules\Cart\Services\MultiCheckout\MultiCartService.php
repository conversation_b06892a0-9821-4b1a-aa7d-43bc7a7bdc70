<?php

namespace App\Modules\Cart\Services\MultiCheckout;

use App\Events\EmailSent;
use App\Exceptions\RegistrationThresholdException;
use App\Mail\DomainRegistrationThreshold;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Cart\Services\CartService;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\MarketPlace\Services\MarketCartService;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Traits\UserContact;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class MultiCartService
{
    use UserContact;

    private $dispatchDelayInSeconds = 180; // three minutes

    private $requestRegisterDomainsKey = 'domains';

    private $requestMarketDomainsKey = 'market_domains';

    public static function instance(): self
    {
        $multiCartService = new self;

        return $multiCartService;
    }

    public function getViewMarketCart()
    {
        $marketData = MarketCartService::instance()->getAllArray();
        $marketIds = MarketCartService::instance()->getAllDomainsId();

        return [
            'domains' => $marketData,
            'domains_list' => $marketIds,
            'settings' => ['transfer_fees' => ExtensionFees::instance()->getDefaultFeesbyType(FeeType::TRANSFER)],
        ];
    }

    public function getMultiCartSummary()
    {
        $registrationData = $this->getRegistrationCheckoutData();

        $marketplaceData = $this->getMarketplaceCheckoutData();

        $otherFeesData = PaymentFeeService::getOtherMulticheckoutFees($registrationData, $marketplaceData);
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($otherFeesData['bill_total'] ?? 0);
        $setupIntents = $this->setPaymentDetails($stripeFeeObj['gross_amount'] ?? $otherFeesData['bill_total']);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        $data['other_fees'] = $otherFeesData;
        $data['secret'] = $setupIntents->client_secret;
        $data['intent'] = $setupIntents->id;
        $data['promise'] = Config::get('stripe.publishable_key');
        $data['account_credit_balance'] = $accountCredit->running_balance ?? 0;
        $data['stripeFeeObj'] = $stripeFeeObj;

        $data['domains'] = $registrationData;
        $data['market_domains'] = $marketplaceData;

        return $data;
    }

    public function getRegistrationCheckoutData()
    {
        $data = CartService::instance()->getMyCart();

        if (empty($data['domains'])) {
            return [];
        }

        $other_fees = $this->getOtherFees($data['domains'], $data['settings']['registration_fees'], FeeType::REGISTRATION);
        $this->checkRegistryBalance($data['domains'], $other_fees, FeeType::REGISTRATION);

        $data['other_fees'] = $other_fees;
        unset($data['cartCollection']);

        return $data;
    }

    public function getMarketplaceCheckoutData(): array
    {
        $marketCarts = MarketCartService::instance()->getAllArray();

        if (empty($marketCarts)) {
            return [];
        }

        $transfer_fees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::TRANSFER);

        $other_fees = $this->getOtherFees($marketCarts, $transfer_fees, FeeType::TRANSFER);
        $this->checkRegistryBalance($marketCarts, $other_fees, FeeType::TRANSFER);
        $other_fees['price_total'] = $this->getMarketPricesTotal($marketCarts);
        $other_fees['premium_total'] = $other_fees['price_total'] + $other_fees['transfer_total'];
        $other_fees['bill_total'] = $other_fees['price_total'] + $other_fees['bill_total'];

        return [
            'domains' => $marketCarts,
            'other_fees' => $other_fees,
            'settings' => ['transfer_fees' => $transfer_fees],
            'user_id' => $this->getUserId(),
        ];
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getOtherFees(array $domains, array $fees, string $type)
    {
        switch ($type) {
            case FeeType::REGISTRATION:
                return PaymentFeeService::getOtherRegisterFees($domains, $fees);
            case FeeType::TRANSFER:
                return PaymentFeeService::getOtherTransferFees($domains, $fees);
            case FeeType::RENEW:
                return PaymentFeeService::getOtherRenewalFees($domains);
            case FeeType::REDEMPTION:
                return PaymentFeeService::getOtherRedemptionFees($domains, $fees);
        }
    }

    private function checkRegistryBalance(array $domains, array $fees, string $type): array
    {
        return RegistryAccountBalanceService::checkRegistryBalance($domains, $fees, $type);
    }

    private function getMarketPricesTotal($marketCarts)
    {
        $priceTotal = 0;

        foreach ($marketCarts as $marketCart) {
            $priceTotal += $marketCart->price;
        }

        return $priceTotal;
    }

    private function setPaymentDetails(float $bill_total): object
    {
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($bill_total);

        return PaymentIntentProvider::instance()->create($payment);
    }

    public function getRegistrationThresholdData()
    {
        $now = Carbon::now(); // current date and time
        $threshold = self::getDomainThreshold();
        [$start, $end] = self::getPeriodRange($threshold->period_type);

        $domainCreateForThePeriod = DB::table('domains')
            // ->whereYear('created_at', $now->year)
            // ->whereMonth('created_at', $now->month)
            ->whereBetween('created_at', [$start, $end])
            ->count();

        return $domainCreateForThePeriod;
    }

    public static function getDomainThreshold()
    {
        return DB::table('domain_threshold')->get(['id', 'threshold_limit', 'send_notif', 'times_triggered', 'period_type'])->first();
    }

    public static function displayThresholdNotif($type = FeeType::REGISTRATION)
    {
        self::sendMaxThresholdEmail();

        $route = [
            FeeType::REGISTRATION => ['route' => route('domain.mycart'), 'label' => 'Go to My Cart'],
            FeeType::RENEW => ['route' => route('domain'), 'label' => 'Go to My Domains'],
            FeeType::TRANSFER => ['route' => route('transfer.inbound.mycart'), 'label' => 'Go to My Transfer Cart'],
        ][$type];

        throw new RegistrationThresholdException(
            503,
            'Domain registrations are currently unavailable. Please try again later.',
            'Temporary Domain Registration Suspension',
            $route['route'],
            $route['label'],
        );
    }

    private static function sendMaxThresholdEmail()
    {
        $domainThreshold = self::getDomainThreshold();

        $payload = [
            'subject' => 'Domain Registration Maximum Threshold',
            'greeting' => 'Greetings!',
            'body' => 'We would like to inform you that the maximum allowed threshold for domain registrations has been reached for your account or the current system cycle.',
            'sender' => 'StrangeDomains Support',
        ];

        Mail::to($domainThreshold->send_notif)->send(new DomainRegistrationThreshold($payload));

        event(new EmailSent(
            $data['user_id'] ?? null,
            $domainThreshold->send_notif,
            $domainThreshold->send_notif,
            'Domain Registration Maximum Threshold',
            'Domain Registration Threshold',
            json_encode($payload),
            null
        ));

        $domainThreshold->times_triggered += 1;
        DB::table('domain_threshold')
            ->where('id', $domainThreshold->id)
            ->update([
                'times_triggered' => $domainThreshold->times_triggered,
                'updated_at' => Carbon::now(), // current date and time
            ]);
    }

    private static function getPeriodRange($period)
    {
        $now = Carbon::now();

        return match ($period) {
            'daily' => [$now->copy()->startOfDay(), $now->copy()->endOfDay()],
            'weekly' => [$now->copy()->startOfWeek(), $now->copy()->endOfWeek()],
            'monthly' => [$now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
            default => [$now->copy()->startOfDay(), $now->copy()->endOfDay()],
        };
    }
}
