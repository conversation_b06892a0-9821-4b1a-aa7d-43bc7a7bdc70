[2025-08-01 06:40:51] local.INFO: mj<PERSON><PERSON><PERSON><PERSON>@gmail.com Created stripe intent: usd 2598  
[2025-08-01 06:41:39] local.INFO: mjb<PERSON><PERSON><PERSON>@gmail.com Created stripe intent: usd 2598  
[2025-08-01 06:41:48] local.INFO: mjb<PERSON><PERSON><PERSON>@gmail.com Created stripe intent: usd 2598  
[2025-08-01 06:41:53] local.ERROR: {"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P10]: Invalid column reference: 7 ERROR:  there is no unique or exclusion constraint matching the ON CONFLICT specification (Connection: pgsql, SQL: insert into \"system_daily_transactions\" (\"counter\", \"date\", \"transaction_id\") values (3, 2025-08-01 06:41:53, 1) on conflict (\"transaction_id\", \"date\") do update set \"counter\" = \"excluded\".\"counter\")","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/multicart","code":"42P10","ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"sy...', Array, Object(Closure))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into \"sy...', Array, Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3945): Illuminate\\Database\\Connection->affectingStatement('insert into \"sy...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService.php(190): Illuminate\\Database\\Query\\Builder->upsert(Array, Array, Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService.php(47): App\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService->upsertDailyTransactions(3)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Cart\\Requests\\MultiCheckoutRequest.php(73): App\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService->validateTransaction('REGISTER', 3, 'pi_3RrCf3AYkyII...')
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(35): App\\Modules\\Cart\\Requests\\MultiCheckoutRequest->passedValidation()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest), Object(Illuminate\\Foundation\\Application))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest), Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Car...', Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Car...', Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Car...', Array, true)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Car...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Car...', Array)
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Car...')
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Cart\\Controllers\\MultiCheckoutController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Cart\\Controllers\\MultiCheckoutController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Cart\\Controllers\\MultiCheckoutController), 'store')
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#80 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#81 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#82 {main}"}  
[2025-08-01 06:45:54] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/multicart. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/multicart","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 07:09:42] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-01 07:09:47] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-01 07:09:52] local.ERROR: {"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P10]: Invalid column reference: 7 ERROR:  there is no unique or exclusion constraint matching the ON CONFLICT specification (Connection: pgsql, SQL: insert into \"system_daily_transactions\" (\"counter\", \"date\", \"transaction_id\") values (3, 2025-08-01 07:09:52, 1) on conflict (\"transaction_id\", \"date\") do update set \"counter\" = \"excluded\".\"counter\")","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/multicart","code":"42P10","ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"sy...', Array, Object(Closure))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('insert into \"sy...', Array, Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3945): Illuminate\\Database\\Connection->affectingStatement('insert into \"sy...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService.php(190): Illuminate\\Database\\Query\\Builder->upsert(Array, Array, Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService.php(47): App\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService->upsertDailyTransactions(3)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Cart\\Requests\\MultiCheckoutRequest.php(73): App\\Modules\\TransactionThreshold\\Services\\TransactionThresholdService->validateTransaction('REGISTER', 3, 'pi_3RrD67AYkyII...')
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(35): App\\Modules\\Cart\\Requests\\MultiCheckoutRequest->passedValidation()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest), Object(Illuminate\\Foundation\\Application))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest), Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Car...', Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Car...', Object(App\\Modules\\Cart\\Requests\\MultiCheckoutRequest))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Car...', Array, true)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Car...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Car...', Array)
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Car...')
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Cart\\Controllers\\MultiCheckoutController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Cart\\Controllers\\MultiCheckoutController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Cart\\Controllers\\MultiCheckoutController), 'store')
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#80 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#81 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#82 {main}"}  
[2025-08-01 07:14:13] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/multicart. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/multicart","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 07:14:13] production.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/multicart. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/multicart","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 07:14:32] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-01 07:14:36] local.INFO: <EMAIL> Created stripe intent: usd 2598  
[2025-08-01 07:14:43] local.INFO: multicheckout request store  
[2025-08-01 07:14:43] local.INFO: <EMAIL> Created Domains: restaurantzxczxc.net,restaurantzxczxc.org,restaurantzxczxc.com  
[2025-08-01 07:14:43] local.INFO: <EMAIL> Domains on process: restaurantzxczxc.net,restaurantzxczxc.org,restaurantzxczxc.com  
[2025-08-01 07:14:43] local.INFO: <EMAIL> Deleted from cart id: [10,11,12]  
[2025-08-01 07:14:43] local.INFO: <EMAIL> Created Registered Domains: restaurantzxczxc.net,restaurantzxczxc.org,restaurantzxczxc.com  
[2025-08-01 07:14:43] local.INFO: getStripeIntentById paymentIntentDetails:: {"id":"pi_3RrDAmAYkyIIc8ES1KraF94Q","object":"payment_intent","amount":2598,"amount_capturable":0,"amount_details":{"tip":[]},"amount_received":2598,"application":null,"application_fee_amount":null,"automatic_payment_methods":{"allow_redirects":"always","enabled":true},"canceled_at":null,"cancellation_reason":null,"capture_method":"manual","client_secret":"pi_3RrDAmAYkyIIc8ES1KraF94Q_secret_EmjLersRufLPwuHwNi2No0yap","confirmation_method":"automatic","created":1754032476,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"invoice":null,"last_payment_error":null,"latest_charge":{"id":"ch_3RrDAmAYkyIIc8ES1eOzmSfy","object":"charge","amount":2598,"amount_captured":2598,"amount_refunded":0,"application":null,"application_fee":null,"application_fee_amount":null,"balance_transaction":{"id":"txn_3RrDAmAYkyIIc8ES1SG1bCQe","object":"balance_transaction","amount":2598,"available_on":1754352000,"balance_type":"payments","created":1754032483,"currency":"usd","description":null,"exchange_rate":null,"fee":105,"fee_details":[{"amount":105,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":2493,"reporting_category":"charge","source":"ch_3RrDAmAYkyIIc8ES1eOzmSfy","status":"pending","type":"charge"},"billing_details":{"address":{"city":null,"country":"PH","line1":null,"line2":null,"postal_code":null,"state":null},"email":null,"name":"Miake","phone":null,"tax_id":null},"calculated_statement_descriptor":"STRANGEDOMAINS.COM","captured":true,"created":1754032477,"currency":"usd","customer":"cus_SjLdqmCcIMvcw8","description":null,"destination":null,"dispute":null,"disputed":false,"failure_balance_transaction":null,"failure_code":null,"failure_message":null,"fraud_details":[],"invoice":null,"livemode":false,"metadata":[],"on_behalf_of":null,"order":null,"outcome":{"advice_code":null,"network_advice_code":null,"network_decline_code":null,"network_status":"approved_by_network","reason":null,"risk_level":"normal","risk_score":9,"seller_message":"Payment complete.","type":"authorized"},"paid":true,"payment_intent":"pi_3RrDAmAYkyIIc8ES1KraF94Q","payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_details":{"card":{"amount_authorized":2598,"authorization_code":"779061","brand":"visa","capture_before":1754637277,"checks":{"address_line1_check":null,"address_postal_code_check":null,"cvc_check":null},"country":"US","exp_month":4,"exp_year":2044,"extended_authorization":{"status":"disabled"},"fingerprint":"nDLplhyCouS3uPAb","funding":"credit","incremental_authorization":{"status":"unavailable"},"installments":null,"last4":"4242","mandate":null,"multicapture":{"status":"unavailable"},"network":"visa","network_token":{"used":false},"network_transaction_id":"110687611210810","overcapture":{"maximum_amount_capturable":2598,"status":"unavailable"},"regulated_status":"unregulated","three_d_secure":null,"wallet":null},"type":"card"},"radar_options":[],"receipt_email":null,"receipt_number":null,"receipt_url":"https:\/\/pay.stripe.com\/receipts\/payment\/CAcaFwoVYWNjdF8xTmFZWk5BWWt5SUljOEVTKOTSscQGMgb8hhNp2q86LBaFBWFqEFNNgtCgsM3oWj--SjX3BK_nm92LhsY3A78oiHPODlCY1SZlCnvT","refunded":false,"review":null,"shipping":null,"source":null,"source_transfer":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null},"livemode":false,"metadata":[],"next_action":null,"on_behalf_of":null,"payment_method":"pm_1RnswDAYkyIIc8ESetsU36Ca","payment_method_configuration_details":{"id":"pmc_1OmiTmAYkyIIc8ESQgh6JQYZ","parent":null},"payment_method_options":{"afterpay_clearpay":{"reference":null},"card":{"installments":null,"mandate_options":null,"network":null,"request_three_d_secure":"automatic"},"cashapp":[],"klarna":{"preferred_locale":null},"link":{"persistent_token":null}},"payment_method_types":["card","afterpay_clearpay","klarna","link","cashapp"],"processing":null,"receipt_email":null,"review":null,"setup_future_usage":null,"shipping":null,"source":null,"statement_descriptor":null,"statement_descriptor_suffix":null,"status":"succeeded","transfer_data":null,"transfer_group":null}  
[2025-08-01 07:14:43] local.INFO: getStripeIntentById balance:: {"id":"txn_3RrDAmAYkyIIc8ES1SG1bCQe","object":"balance_transaction","amount":2598,"available_on":1754352000,"balance_type":"payments","created":1754032483,"currency":"usd","description":null,"exchange_rate":null,"fee":105,"fee_details":[{"amount":105,"application":null,"currency":"usd","description":"Stripe processing fees","type":"stripe_fee"}],"net":2493,"reporting_category":"charge","source":"ch_3RrDAmAYkyIIc8ES1eOzmSfy","status":"pending","type":"charge"}  
[2025-08-01 07:14:43] local.INFO: <EMAIL> getStripeFeesOnPayment data:: {"name":"Payment Invoice","type":"MULTI_CHECKOUT_INVOICE","paid_amount":24.54,"total_amount":24.54,"gross_amount":25.98,"bill_amount":24.54,"payment_intent":"pi_3RrDAmAYkyIIc8ES1KraF94Q","source":"STRIPE","user_id":7,"total_payment_node":3,"net_amount":24.93,"service_fee":1.05,"adjustments":0.39,"charge_id":"ch_3RrDAmAYkyIIc8ES1eOzmSfy"}  
[2025-08-01 07:14:43] local.INFO: <EMAIL> getStripeFeesOnPayment stripeFees:: {"gross_amount":25.98,"net_amount":24.93,"service_fee":1.05,"charge_id":"ch_3RrDAmAYkyIIc8ES1eOzmSfy","payment_intent":"pi_3RrDAmAYkyIIc8ES1KraF94Q"}  
[2025-08-01 07:14:43] local.INFO: <EMAIL> Created payment invoice with value 24,24.54,PAID,3,35  
[2025-08-01 07:14:43] local.INFO: <EMAIL> Created payment invoice with value {"total_amount":24,"paid_amount":24.54,"status":"PAID","total_payment_node":3,"payment_service_id":35,"updated_at":"2025-08-01T07:14:43.000000Z","created_at":"2025-08-01T07:14:43.000000Z","id":24}  
[2025-08-01 07:14:44] local.INFO: <EMAIL> Updated stripe intent for : 7-24-3  
[2025-08-01 07:14:44] local.INFO: <EMAIL> Created payment nodes for  3 domains.  
[2025-08-01 07:14:44] local.INFO: <EMAIL> Created payment node invoices for  3 nodes.  
[2025-08-01 07:14:44] local.INFO: Payment summary created: Payment Invoice  
[2025-08-01 07:14:51] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route domain\/multicart. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/multicart","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 08:03:13] local.INFO: <EMAIL> logging out at 127.0.0.1  
[2025-08-01 08:03:22] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/email\/send-code","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationSendCodeFormRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationSendCodeFormRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationSendCodeFormRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationSendCodeFormRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationEmailVerificationController), 'sendCodeToEmail')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationEmailVerificationController), 'sendCodeToEmail')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationEmailVerificationController), 'sendCodeToEmail')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:03:57] local.ERROR: {"error":"Illuminate\\Validation\\ValidationException","message":"Code is incorrect","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/email\/verify-code\/KY9eZO3alCZzX6zxeJJByvpTvXCZdc9z","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(32): Illuminate\\Foundation\\Http\\FormRequest->failedValidation(Object(Illuminate\\Validation\\Validator))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationCodeFormRequest), Object(Illuminate\\Foundation\\Application))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationCodeFormRequest), Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationCodeFormRequest))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationEmailVerificationCodeFormRequest))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationEmailVerificationController), 'verifyEmailCode')
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationEmailVerificationController), 'verifyEmailCode')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationEmailVerificationController), 'verifyEmailCode')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}"}  
[2025-08-01 08:04:28] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:04:33] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:04:39] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:04:41] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:04:49] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:04:57] local.ERROR: {"error":"Illuminate\\Validation\\ValidationException","message":"The password field confirmation does not match.","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(32): Illuminate\\Foundation\\Http\\FormRequest->failedValidation(Object(Illuminate\\Validation\\Validator))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Object(Illuminate\\Foundation\\Application))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Use...', Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Use...', Array, true)
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Use...', Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Use...', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Use...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}"}  
[2025-08-01 08:05:12] local.ERROR: {"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column \"admin_id\" of relation \"admin_notifications\" violates not-null constraint
DETAIL:  Failing row contains (10, Client \"<EMAIL>\" was registered. Go to the Client p..., New Client Registered, null, client, 2025-08-01 08:05:12, 2025-08-01 08:05:12, medium, null). (Connection: pgsql, SQL: insert into \"admin_notifications\" (\"title\", \"message\", \"redirect_url\", \"importance\", \"created_at\", \"updated_at\") values (New Client Registered, Client \"<EMAIL>\" was registered. Go to the Client page to take appropriate action., client, medium, 2025-08-01 08:05:12, 2025-08-01 08:05:12))","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":"23502","ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"ad...', Array, Object(Closure))
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"ad...', Array, Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"ad...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3747): Illuminate\\Database\\Connection->insert('insert into \"ad...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\AdminNotification\\Services\\AdminNotificationService.php(21): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\AdminNotification\\Services\\AdminNotificationService.php(49): App\\Modules\\AdminNotification\\Services\\AdminNotificationService->sendNotification(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\UserRegistration\\Services\\UserRegistrationService.php(75): App\\Modules\\AdminNotification\\Services\\AdminNotificationService->sendClientRegistrationNotif(Array)
#7 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\UserRegistration\\Controllers\\UserRegistrationController.php(53): App\\Modules\\UserRegistration\\Services\\UserRegistrationService->createUser(Array, 'Or932a9Zubs57Oe...')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController->create(Object(App\\Modules\\UserRegistration\\Requests\\UserRegistrationCreateFormRequest), 'Or932a9Zubs57Oe...')
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('create', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\UserRegistration\\Controllers\\UserRegistrationController), 'create')
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 {main}"}  
[2025-08-01 08:06:56] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 08:07:00] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\MethodNotAllowedHttpException","message":"The GET method is not supported for route registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create. Supported methods: POST.","url":"http:\/\/www.mydomain.strangedomains.local\/registration\/Or932a9Zubs57OeUUH9mdwdTQfA8SXUf\/create","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(108): Illuminate\\Routing\\AbstractRouteCollection->requestMethodNotAllowed(Object(Illuminate\\Http\\Request), Array, 'GET')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php(42): Illuminate\\Routing\\AbstractRouteCollection->getRouteForMethods(Object(Illuminate\\Http\\Request), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#26 {main}"}  
[2025-08-01 08:07:30] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/login","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Aut...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Aut...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Aut...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Aut...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-01 08:07:30] local.INFO: <EMAIL> successful login at 127.0.0.1  
[2025-08-01 09:26:05] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local\/domain","code":0,"ip":"127.0.0.1","method":"GET","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 {main}"}  
[2025-08-04 01:10:00] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/login","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Aut...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Aut...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Aut...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Aut...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-04 01:10:01] local.INFO: <EMAIL> successful login at 127.0.0.1  
[2025-08-04 04:14:42] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route Culture_New\/user\/auth\/login.php could not be found.","url":"http:\/\/localhost\/Culture_New\/user\/auth\/login.php","code":0,"ip":"::1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteCollection.php(162): Illuminate\\Routing\\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\\Http\\Request), NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(763): Illuminate\\Routing\\RouteCollection->match(Object(Illuminate\\Http\\Request))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->findRoute(Object(Illuminate\\Http\\Request))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}"}  
[2025-08-04 04:14:44] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/localhost\/Culture_New\/user\/auth\/login.php","code":0,"ip":"::1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\app\\Exceptions\\Handler.php(114): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(688): App\\Exceptions\\Handler->App\\Exceptions\\{closure}(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException), Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(589): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 {main}"}  
[2025-08-04 07:50:08] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-08-04 07:50:12] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-08-04 07:50:12] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-08-04 08:25:38] local.ERROR: {"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-client\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-client\\resources\\views\\app.blade.php)","url":"http:\/\/www.mydomain.strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteManifestNotFoundException), 1)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\1xampp\\\\htdoc...', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\1xampp\\\\htdoc...', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(126): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 {main}"}  
[2025-08-04 08:27:02] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-04 08:27:02] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-04 08:27:02] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-04 08:27:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:27:03] local.INFO: GeneralNotification: done  
[2025-08-04 08:27:04] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:27:04] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:27:04] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:27:04] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:27:05] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:27:05] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:27:06] local.ERROR: {"error":"Symfony\\Component\\HttpKernel\\Exception\\HttpException","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/login","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Auth\\Requests\\LoginRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Aut...', Object(App\\Modules\\Auth\\Requests\\LoginRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Aut...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Aut...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Aut...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Aut...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Auth\\Controllers\\AuthenticatedSessionController), 'store')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-04 08:27:07] local.INFO: <EMAIL> successful login at 127.0.0.1  
[2025-08-04 08:28:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:28:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:28:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:28:02] local.INFO: GeneralNotification: done  
[2025-08-04 08:28:03] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:28:03] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:28:03] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:28:03] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:28:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:28:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:28:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:28:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:28:05] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:28:05] local.INFO: AccountCreditor: Done  
[2025-08-04 08:28:09] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:28:13] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:28:15] local.INFO: SessionPollChecker: Updating poll list...  
[2025-08-04 08:28:15] local.INFO: AfternicUpdateTransferFromPoll: Starting ...  
[2025-08-04 08:28:15] local.INFO: AfternicUpdateTransferFromPoll: Terminating ...  
[2025-08-04 08:28:15] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:29:01] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:29:01] local.INFO: GeneralNotification: done  
[2025-08-04 08:29:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:29:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:29:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:29:01] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:29:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:29:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:29:30] local.INFO: <EMAIL> Created stripe intent: usd 5280  
[2025-08-04 08:29:30] local.ERROR: {"error":"ErrorException","message":"Undefined property: stdClass::$tld_id","url":"http:\/\/www.mydomain.strangedomains.local\/domain-redemption\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\1xampp\\\\htdoc...', 205)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(205): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined prope...', 'C:\\\\1xampp\\\\htdoc...', 205)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(196): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::calculateTotalRegistryAmount(Array, Array, 'redemption_fee', Array, Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(106): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::getTotalRegistryAmount(Array, Array, 'REDEMPTION')
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Services\\DomainRedemptionService.php(122): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::checkRegistryBalance(Array, Array, 'REDEMPTION', 'pi_3RsJlpAYkyII...')
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest.php(40): App\\Modules\\DomainRedemption\\Services\\DomainRedemptionService->getRedemptionPaymentData(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController.php(41): App\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest->getRedemptionPaymentData()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController->pay(Object(App\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController), 'pay')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-04 08:30:01] local.INFO: DomainExpiryEvaluator: Running...  
[2025-08-04 08:30:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:30:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:30:02] local.INFO: PostAutoRenewalGracePeriodHandler: Checking for domains...  
[2025-08-04 08:30:02] local.INFO: PostAutoRenewalGracePeriodHandler: No domains found...  
[2025-08-04 08:30:02] local.INFO: PostAutoRenewalGracePeriodHandler: Done  
[2025-08-04 08:30:03] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-04 08:30:03] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-04 08:30:03] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-04 08:30:04] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:30:04] local.INFO: GeneralNotification: done  
[2025-08-04 08:30:05] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:30:05] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:30:05] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:30:05] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:30:05] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:30:05] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:30:06] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:30:06] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:30:07] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:30:07] local.INFO: AccountCreditor: Done  
[2025-08-04 08:30:08] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:30:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:30:09] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:30:09] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:31:01] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:31:01] local.INFO: GeneralNotification: done  
[2025-08-04 08:31:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:31:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:31:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:31:02] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:31:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:31:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:32:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:32:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:32:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:32:02] local.INFO: GeneralNotification: done  
[2025-08-04 08:32:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:32:03] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:32:03] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:32:03] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:32:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:32:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:32:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:32:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:32:05] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:32:05] local.INFO: AccountCreditor: Done  
[2025-08-04 08:32:06] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:32:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:32:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:32:08] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:33:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-04 08:33:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-04 08:33:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-04 08:33:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:33:02] local.INFO: GeneralNotification: done  
[2025-08-04 08:33:03] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:33:03] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:33:03] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:33:03] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:33:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:33:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:34:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:34:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:34:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:34:02] local.INFO: GeneralNotification: done  
[2025-08-04 08:34:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:34:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:34:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:34:02] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:34:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:34:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:34:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:34:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:34:05] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:34:05] local.INFO: AccountCreditor: Done  
[2025-08-04 08:34:06] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:34:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:34:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:34:07] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:35:01] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:35:01] local.INFO: GeneralNotification: done  
[2025-08-04 08:35:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:35:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:35:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:35:02] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:35:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:35:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:36:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:36:02] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:36:03] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-04 08:36:03] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-04 08:36:03] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-04 08:36:04] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:36:04] local.INFO: GeneralNotification: done  
[2025-08-04 08:36:06] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:36:06] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:36:06] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:36:06] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:36:07] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:36:07] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:36:08] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:36:08] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:36:09] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:36:09] local.INFO: AccountCreditor: Done  
[2025-08-04 08:36:10] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:36:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:36:11] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:36:11] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:36:45] local.INFO: <EMAIL> Created stripe intent: usd 20971  
[2025-08-04 08:37:01] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:37:01] local.INFO: GeneralNotification: done  
[2025-08-04 08:37:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:37:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:37:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:37:02] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:37:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:37:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:38:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:38:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:38:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:38:02] local.INFO: GeneralNotification: done  
[2025-08-04 08:38:03] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:38:03] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:38:03] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:38:03] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:38:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:38:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:38:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:38:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:38:05] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:38:05] local.INFO: AccountCreditor: Done  
[2025-08-04 08:38:06] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:38:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:38:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:38:07] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:39:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-04 08:39:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-04 08:39:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-04 08:39:02] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:39:02] local.INFO: GeneralNotification: done  
[2025-08-04 08:39:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:39:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:39:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:39:02] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:39:03] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:39:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:40:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:40:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:40:01] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:40:01] local.INFO: GeneralNotification: done  
[2025-08-04 08:40:02] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:40:02] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:40:02] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:40:02] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:40:03] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:40:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:40:04] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:40:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:40:05] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:40:05] local.INFO: AccountCreditor: Done  
[2025-08-04 08:40:06] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:40:06] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:40:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:40:07] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:41:01] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:41:01] local.INFO: GeneralNotification: done  
[2025-08-04 08:41:01] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:41:01] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:41:01] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:41:01] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:41:02] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:41:02] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:42:01] local.INFO: JobRetryScheduler: Running...  
[2025-08-04 08:42:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-08-04 08:42:03] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-08-04 08:42:03] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-08-04 08:42:03] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-08-04 08:42:04] local.INFO: GeneralNotification: Running...  
[2025-08-04 08:42:04] local.INFO: GeneralNotification: done  
[2025-08-04 08:42:05] local.INFO: AfternicDomainTask: Running... checking market  
[2025-08-04 08:42:05] local.INFO: AfternicDomainTask: marketplace empty, nothing to process...checking offers  
[2025-08-04 08:42:05] local.INFO: AfternicDomainTask: offer empty, nothing to process..  
[2025-08-04 08:42:05] local.INFO: AfternicDomainTask: done  
[2025-08-04 08:42:06] local.INFO: AfternicJobRetry: Running...  
[2025-08-04 08:42:06] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-08-04 08:42:07] local.INFO: AfternicManualTransfer: Running...  
[2025-08-04 08:42:07] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-08-04 08:42:08] local.INFO: AccountCreditor: Running...  
[2025-08-04 08:42:08] local.INFO: AccountCreditor: Done  
[2025-08-04 08:42:09] local.INFO: SessionPollChecker: Running...  
[2025-08-04 08:42:10] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:42:10] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-08-04 08:42:10] local.INFO: SessionPollChecker: Done  
[2025-08-04 08:42:18] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}"}  
[2025-08-04 08:42:18] production.ERROR: {"error":"RuntimeException","message":"Unsupported cipher or incorrect key length. Supported ciphers are: aes-128-cbc, aes-256-cbc, aes-128-gcm, aes-256-gcm.","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\Encrypter->__construct('APP_KEY', 'AES-256-CBC')
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#15 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#16 {main}"}  
[2025-08-04 08:42:18] production.ERROR: {"error":"Symfony\\Component\\ErrorHandler\\Error\\FatalError","message":"Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:387) in C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'C:\\\\1xampp\\\\htdoc...', 322)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP\/1.1 500 In...', true, 500)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(RuntimeException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(RuntimeException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(RuntimeException))
#8 {main}
  thrown","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-08-04 08:42:21] local.INFO: <EMAIL> Created stripe intent: usd 5280  
[2025-08-04 08:42:21] local.ERROR: {"error":"ErrorException","message":"Trying to access array offset on value of type float","url":"http:\/\/www.mydomain.strangedomains.local\/domain-redemption\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\1xampp\\\\htdoc...', 209)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\1xampp\\\\htdoc...', 209)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(196): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::calculateTotalRegistryAmount(Array, Array, 'redemption_fee', Array, Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(106): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::getTotalRegistryAmount(Array, Array, 'REDEMPTION')
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Services\\DomainRedemptionService.php(125): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::checkRegistryBalance(Array, Array, 'REDEMPTION', 'pi_3RsJyLAYkyII...')
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest.php(40): App\\Modules\\DomainRedemption\\Services\\DomainRedemptionService->getRedemptionPaymentData(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController.php(41): App\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest->getRedemptionPaymentData()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController->pay(Object(App\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController), 'pay')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-04 08:42:55] local.INFO: <EMAIL> Created stripe intent: usd 5280  
[2025-08-04 08:42:55] local.ERROR: {"error":"ErrorException","message":"Trying to access array offset on value of type float","url":"http:\/\/www.mydomain.strangedomains.local\/domain-redemption\/pay","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\\\1xampp\\\\htdoc...', 209)
#1 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(209): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Trying to acces...', 'C:\\\\1xampp\\\\htdoc...', 209)
#2 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(196): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::calculateTotalRegistryAmount(Array, Array, 'redemption_fee', Array, Array)
#3 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\Epp\\Services\\RegistryAccountBalanceService.php(106): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::getTotalRegistryAmount(Array, Array, 'REDEMPTION')
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Services\\DomainRedemptionService.php(125): App\\Modules\\Epp\\Services\\RegistryAccountBalanceService::checkRegistryBalance(Array, Array, 'REDEMPTION', 'pi_3RsJytAYkyII...')
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest.php(40): App\\Modules\\DomainRedemption\\Services\\DomainRedemptionService->getRedemptionPaymentData(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController.php(41): App\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest->getRedemptionPaymentData()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController->pay(Object(App\\Modules\\DomainRedemption\\Requests\\RedemptionPayRequest))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('pay', Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\DomainRedemption\\Controllers\\DomainRedemptionController), 'pay')
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 {main}"}  
[2025-08-04 08:45:23] local.INFO: <EMAIL> Created stripe intent: usd 5280  
[2025-08-04 08:45:42] local.ERROR: HTTP request returned status code 417:
{"status":"error","message":"keyword parameter not found or invalid."}
 ; {"status":"error","message":"keyword parameter not found or invalid."}  
[2025-08-04 08:45:42] local.ERROR: {"error":"Illuminate\\Http\\Client\\RequestException","message":"HTTP request returned status code 417:
{\"status\":\"error\",\"message\":\"keyword parameter not found or invalid.\"}
","url":"http:\/\/www.mydomain.strangedomains.local\/domain\/basicsearch","code":417,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\Response.php(335): Illuminate\\Http\\Client\\Response->toException()
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(929): Illuminate\\Http\\Client\\Response->throw(Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(Illuminate\\Http\\Client\\Response))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(912): tap(Object(Illuminate\\Http\\Client\\Response), Object(Closure))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(910): retry(0, Object(Closure), 100, Object(Closure))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(777): Illuminate\\Http\\Client\\PendingRequest->send('GET', 'https:\/\/strange...', Array)
#7 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\MarketPlace\\Requests\\MarketBasicRequest.php(33): Illuminate\\Http\\Client\\PendingRequest->get('basic_search?do...')
#8 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\DomainEppSearch\\Controllers\\SearchController.php(38): App\\Modules\\MarketPlace\\Requests\\MarketBasicRequest->doBasicSearch()
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\DomainEppSearch\\Controllers\\SearchController->basicSearch(Object(App\\Modules\\MarketPlace\\Requests\\MarketBasicRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('basicSearch', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\DomainEppSearch\\Controllers\\SearchController), 'basicSearch')
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(160): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(91): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '20', '1')
#18 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 {main}"}  
[2025-08-04 08:45:45] local.INFO: <EMAIL> 1 items added to cart.  
