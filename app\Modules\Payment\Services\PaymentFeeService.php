<?php

namespace App\Modules\Payment\Services;

use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\DomainFees;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Setting\Services\Settings;
use App\Traits\CursorPaginate;
use App\Util\Helper\Domain\DomainTld;
use Carbon\Carbon;

class PaymentFeeService
{
    use CursorPaginate, UserLoggerTrait;

    public static function getIcannFee(): float
    {
        return floatval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_ICANN_FEE));
    }

    public static function getOtherRegisterFees(array $domains, array $register_fee): array
    {
        $icann_fee = self::getIcannFee();

        $domain_count = count($domains);
        $privacy_count = 0;
        $year_sum = array_sum(array_column($domains, 'year_length'));
        $register_total = self::getFeesTotal([
            'domains' => $domains,
            'fee' => $register_fee,
        ]);

        $icann_total = $year_sum * $icann_fee;
        $bill_total = $register_total + $icann_total;

        $other_fees = [
            'domain_count' => $domain_count,
            'privacy_count' => $privacy_count,
            'bill_total' => round($bill_total, 2),
            'icann_fee' => round($icann_total, 2),
            'year_sum' => $year_sum,
            'register_total' => round($register_total, 2),
            'register_fee' => $register_fee,
        ];

        return $other_fees;
    }

    public static function getOtherRenewalFees(array $domains): array
    {
        $icann_fee = self::getIcannFee();

        $domain_count = count($domains);
        $privacy_count = 0;
        $year_sum = array_sum(array_column($domains, 'year_length'));
        $renewal_fee = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::RENEW);
        $late_renewal_penalty = DomainFees::instance()->getFee(FeeType::PENALTY_LATE_RENEWAL)->value;
        $gracePeriod = intval(value: Settings::instance()->getValueByKey(SettingKey::DOMAIN_GRACE_PERIOD));
        $domains = json_decode(json_encode($domains), false);

        $renewal_total = self::getFeesTotal([
            'domains' => $domains,
            'fee' => $renewal_fee,
        ]);

        $penalty_total = self::getPenaltyTotal([
            'domains' => $domains,
            'penalty' => $late_renewal_penalty,
            'gracePeriod' => $gracePeriod,
        ]);

        $icann_total = $year_sum * $icann_fee;
        $bill_total = $renewal_total + $icann_total + $penalty_total;

        $other_fees = [
            'domain_count' => $domain_count,
            'privacy_count' => $privacy_count,
            'bill_total' => round($bill_total, 2),
            'icann_fee' => round($icann_total, 2),
            'year_sum' => $year_sum,
            'renewal_total' => round($renewal_total, 2),
            'penalty_total' => round($penalty_total, 2),
            'renewal_fee' => $renewal_fee,
        ];

        return $other_fees;
    }

    public static function getOtherTransferFees(array $domains, array $transfer_fee): array
    {
        $icann_fee = self::getIcannFee();

        $domain_count = count($domains);
        $privacy_count = 0;
        $year_sum = array_sum(array_column($domains, 'year_length'));

        if ($year_sum == 0) {
            $year_sum = $domain_count;
        }

        $transfer_total = self::getFeesTotal([
            'domains' => $domains,
            'fee' => $transfer_fee,
        ]);

        $icann_total = $year_sum * $icann_fee;
        $bill_total = $transfer_total + $icann_total;

        $other_fees = [
            'domain_count' => $domain_count,
            'privacy_count' => $privacy_count,
            'bill_total' => round($bill_total, 2),
            'icann_fee' => round($icann_total, 2),
            'year_sum' => $year_sum,
            'transfer_total' => round($transfer_total, 2),
            'transfer_fee' => $transfer_fee,
        ];

        return $other_fees;
    }

    public static function getOtherRedemptionFees(array $domains, array $redemption_fee): array
    {
        $icann_fee = self::getIcannFee();

        $domain_count = count($domains);
        $privacy_count = 0;
        $year_sum = array_sum(array_column($domains, 'year_length'));

        if ($year_sum == 0) {
            $year_sum = $domain_count;
        }

        $redemption_total = self::getFeesTotal([
            'domains' => $domains,
            'fee' => $redemption_fee,
        ]);

        $icann_total = $year_sum * $icann_fee;
        $bill_total = $redemption_total + $icann_total;

        $other_fees = [
            'domain_count' => $domain_count,
            'privacy_count' => $privacy_count,
            'bill_total' => round($bill_total, 2),
            'icann_fee' => round($icann_total, 2),
            'year_sum' => $year_sum,
            'redemption_total' => round($redemption_total, 2),
            'redemption_fee' => $redemption_fee,
        ];

        return $other_fees;
    }

    public static function getOtherMulticheckoutFees(array $registrationData, array $marketplaceData)
    {
        $totalDomainCount = 0;
        $totalPrivacyCount = 0;
        $totalBill = 0;
        $totalIcannFee = 0;
        $yearSum = 0;

        if (! empty($registrationData)) {
            $totalDomainCount += $registrationData['other_fees']['domain_count'] ?? 0;
            $totalPrivacyCount += $registrationData['other_fees']['privacy_count'] ?? 0;
            $totalBill += $registrationData['other_fees']['bill_total'] ?? 0;
            $totalIcannFee += $registrationData['other_fees']['icann_fee'] ?? 0;
            $yearSum += $registrationData['other_fees']['year_sum'] ?? 0;
        }

        if (! empty($marketplaceData)) {
            $totalDomainCount += $marketplaceData['other_fees']['domain_count'] ?? 0;
            $totalPrivacyCount += $marketplaceData['other_fees']['privacy_count'] ?? 0;
            $totalBill += $marketplaceData['other_fees']['bill_total'] ?? 0;
            $totalIcannFee += $marketplaceData['other_fees']['icann_fee'] ?? 0;
            $yearSum += $marketplaceData['other_fees']['year_sum'] ?? 0;
        }

        return [
            'domain_count' => $totalDomainCount ?? 0,
            'privacy_count' => $totalPrivacyCount ?? 0,
            'bill_total' => round($totalBill, 2),
            'icann_fee' => round($totalIcannFee, 2),
            'year_sum' => $yearSum ?? 0,
            'register_total' => $registrationData['other_fees']['register_total'] ?? 0,
            'register_fee' => $registrationData['other_fees']['register_fee'] ?? 0,
            'transfer_total' => $marketplaceData['other_fees']['transfer_total'] ?? 0,
            'transfer_fee' => $marketplaceData['other_fees']['transfer_fee'] ?? 0,
        ];
    }

    // PRIVATE FUNCTIONS
    private static function getFeesTotal(array $data): float
    {
        $domains = $data['domains'];
        $fee = $data['fee'];
        $fee_total = 0;
        $tlds = DomainTld::getTldsById();

        foreach ($domains as $domain) {
            $tld_id = $domain->tld_id;
            $extension = $tlds[$tld_id]->extension;
            $price = $fee[$extension]->price ?? 0;
            $year_length = $domain->year_length ?? 1;

            $year_price = intval($year_length) * floatval($price);
            $fee_total += $year_price;
        }

        return $fee_total;
    }

    private static function hasPenalty($domainExpiry, $gracePeriod): bool
    {
        $currentDate = Carbon::now();
        $expiryDate = Carbon::createFromTimestampMs($domainExpiry);
        $penaltyStartDate = $expiryDate->addDays($gracePeriod);

        return $currentDate->greaterThan($penaltyStartDate);
    }

    private static function getPenaltyTotal(array $data): float
    {
        $domains = $data['domains'];
        $penalty = $data['penalty'];
        $gracePeriod = $data['gracePeriod'];

        return array_reduce($domains, function ($total, $domain) use ($gracePeriod, $penalty) {
            if ($domain->status === DomainStatus::EXPIRED && self::hasPenalty($domain->expiry, $gracePeriod)) {
                $total += $penalty;
            }

            return $total;
        }, initial: 0);
    }
}
